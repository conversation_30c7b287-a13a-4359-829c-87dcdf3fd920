<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Computer Shop{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <img src="{{ url_for('static', filename='icons/logo.jpg') }}" alt="Russeykeo Computer Logo" class="sidebar-logo">
                    <h3 class="sidebar-title">Russeykeo Computer</h3>
                </div>
            </div>
            <div class="sidebar-profile">
                <i class="fas fa-user-circle"></i>
                <h3>Hello, {{ session.username }}</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="{{ url_for('auth.staff_dashboard') }}"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="{{ url_for('auth.staff_inventory') }}"><i class="fas fa-boxes"></i> Product</a></li>
                    <li><a href="{{ url_for('auth.staff_discounts') }}"><i class="fas fa-percentage"></i> Discounts</a></li>
                    <li><a href="{{ url_for('auth.staff_orders') }}"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="/auth/staff/walk-in-sales"><i class="fas fa-store"></i> Walk-in Sales</a></li>
                    <li><a href="{{ url_for('staff_preorders') }}"><i class="fas fa-clock"></i> Pre-Orders</a></li>
                    <li><a href="{{ url_for('auth.staff_customers') }}" class="customer-link"><i class="fas fa-users"></i> Customers</a></li>
                    <!-- <li><a href="{{ url_for('auth.staff_suppliers') }}"><i class="fas fa-truck"></i> Suppliers</a></li> -->
                    <li><a href="{{ url_for('auth.staff_reports') }}"><i class="fas fa-chart-bar"></i> Reports</a></li>

                    <!-- Admin and Super Admin only -->
                    {% if session.get('role') in ['admin', 'super_admin'] %}
                    <li><a href="{{ url_for('auth.staff_categories') }}"><i class="fas fa-tags"></i> Categories</a></li>
                    {% endif %}

                    <!-- Super Admin only -->
                    {% if session.get('role') == 'super_admin' %}
                    <li><a href="{{ url_for('auth.staff_users') }}"><i class="fas fa-user-cog"></i> User Management</a></li>
                    {% endif %}
                </ul>
                <div class="logout-link">
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-danger"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
            </nav>
        </div>

        <main class="main-content">
            {% block content %}{% endblock %}
        </main>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    {% if session.get('role') in ['staff', 'admin', 'super_admin'] %}
    <script src="{{ url_for('static', filename='js/staff.js') }}"></script>
    {% endif %}
    {% block scripts %}{% endblock %}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const links = document.querySelectorAll(".sidebar-nav a");
            const currentPath = window.location.pathname;

            links.forEach(link => {
                link.classList.remove("active");
                if (link.getAttribute("href") === currentPath) {
                    link.classList.add("active");
                }
            });
        });
    </script>

    <style>
        :root {
            --primary: #3498db;
            --secondary: #f8f9fa;
            --dark: #212529;
            --light: #ffffff;
            --sidebar-width: 250px;
            --transition-speed: 0.3s;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--secondary);
            margin: 0;
            min-height: 100vh;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--dark);
            color: var(--light);
            position: fixed;
            left: 0;
            top: 0;
            height: 100%;
            z-index: 1000;
            transition: transform var(--transition-speed) ease;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 1rem 1.5rem;
            background: #1a252f;
            text-align: center;
        }

        .sidebar-brand {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--light);
            line-height: 1.2;
            text-align: center;
        }

        .sidebar-profile {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid #444;
        }

        .sidebar-profile i {
            font-size: 2.5rem;
            color: #ccc;
            margin-bottom: 0.5rem;
        }

        .sidebar-profile h3 {
            font-size: 1rem;
            margin: 0;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .sidebar-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav li a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            color: #b8c7ce;
            text-decoration: none;
            transition: all var(--transition-speed);
        }

        .sidebar-nav li a:hover,
        .sidebar-nav li a.active {
            color: var(--light);
            background: #1e282c;
        }

        .sidebar-nav li a i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }

        .logout-link {
            padding: 1rem;
            border-top: 1px solid #3a4a5a;
        }

        .logout-link .btn-danger {
            width: 100%;
            padding: 0.5rem;
            font-size: 0.9rem;
            background-color: #eb0606;
            border-color: #eb0606;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 1.5rem;
            flex-grow: 1;
            transition: margin-left var(--transition-speed);
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            bottom: 1.25rem;
            right: 1.25rem;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            color: var(--light);
            z-index: 1000;
            animation: fadeIn var(--transition-speed), fadeOut var(--transition-speed) 2.7s;
        }

        .toast-success { background-color: #28a745; }
        .toast-error { background-color: #dc3545; }
        .toast-info { background-color: #17a2b8; }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(1.25rem); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('/static/images/tech-bg.jpg');
            background-size: cover;
            color: var(--light);
            padding: 4rem 0;
            text-align: center;
        }

        /* Cards */
        .category-card, .product-card {
            border: none;
            transition: all var(--transition-speed);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .category-card:hover, .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .category-card img {
            transition: transform 0.5s;
        }

        .category-card:hover img {
            transform: scale(1.1);
        }

        /* Login Container */
        .login-container {
            background: var(--light);
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            margin: 2rem auto;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #34495e;
            font-weight: 500;
        }

        input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            box-sizing: border-box;
        }

        input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        button {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--primary);
            color: var(--light);
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color var(--transition-speed);
        }

        button:hover {
            background-color: #2980b9;
        }

        .flash-messages {
            color: #e74c3c;
            margin-bottom: 1rem;
            text-align: center;
            font-size: 0.9rem;
        }

        .back-button {
            color: var(--primary);
            text-decoration: none;
            font-size: 1rem;
            transition: color var(--transition-speed);
        }

        .back-button:hover {
            color: #2980b9;
        }

        /* Responsive Design */
        @media (max-width: 767.98px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding-top: 3.75rem;
            }

            .hero-section {
                padding: 2rem 0;
            }
        }
    </style>
</body>
</html>