{% extends "base.html" %}

{% block title %}Category Management{% endblock %}

{% block content %}
<div class="staff-container">
    <h1>Category Management</h1>

    <!-- Add Category Form -->
    <div class="inventory-actions">
        <div class="inventory-search">
            <form method="post" style="display: flex; gap: 10px; align-items: end; width: 100%;">
                <div style="flex: 1;">
                    <label for="name" style="display: block; margin-bottom: 5px; font-weight: bold;">Category Name:</label>
                    <input type="text" id="name" name="name" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <div style="flex: 2;">
                    <label for="description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description (Optional):</label>
                    <input type="text" id="description" name="description" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <button type="submit" style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; min-width: 120px;">Add Category</button>
            </form>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="inventory-list">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #aaa; font-weight: 400;">
            <thead>
                <tr>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">ID</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Category Name</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Description</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Products Count</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for category in categories %}
                <tr>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">{{ category.id }}</td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="name-display">{{ category.name }}</span>
                        <input type="text" class="name-edit" value="{{ category.name }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="description-display">{{ category.description or 'No description' }}</span>
                        <input type="text" class="description-edit" value="{{ category.description or '' }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        <span id="product-count-{{ category.id }}">Loading...</span>
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        <div class="action-buttons">
                            <button class="edit-btn" onclick="editCategory({{ category.id }})" style="background-color: #ffc107; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Edit</button>
                            <button class="save-btn" onclick="saveCategory({{ category.id }})" style="display: none; background-color: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Save</button>
                            <button class="cancel-btn" onclick="cancelEdit({{ category.id }})" style="display: none; background-color: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Cancel</button>
                            <button onclick="deleteCategory({{ category.id }}, {{ category.name|tojson|safe }})" style="background-color: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px;">Delete</button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Message Container for Notifications -->
<div id="message-container"></div>

{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
<style>
    .staff-container {
        width: 100%;
        margin: 0 auto;
        padding: 20px;
    }

    .inventory-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 20px 0;
        gap: 15px;
    }

    .inventory-search {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 1000px;
    }

    .inventory-list table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .inventory-list th,
    .inventory-list td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .inventory-list th {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .inventory-list tbody tr:hover {
        background-color: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
    }

    .action-buttons button {
        transition: background-color 0.2s;
    }

    .action-buttons button:hover {
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
<script>
// Load product counts for each category
document.addEventListener('DOMContentLoaded', function() {
    loadProductCounts();
});

function loadProductCounts() {
    const categoryRows = document.querySelectorAll('tbody tr');
    categoryRows.forEach(row => {
        const categoryId = row.querySelector('button[onclick*="editCategory"]').onclick.toString().match(/\d+/)[0];
        const countElement = document.getElementById(`product-count-${categoryId}`);

        fetch(`/api/staff/categories/${categoryId}/products/count`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    countElement.textContent = data.count;
                } else {
                    countElement.textContent = '0';
                }
            })
    });
}

function editCategory(categoryId) {
    const row = document.querySelector(`tr:has(button[onclick*="editCategory(${categoryId})"])`);

    // Hide display elements and show edit elements
    row.querySelector('.name-display').style.display = 'none';
    row.querySelector('.name-edit').style.display = 'inline';
    row.querySelector('.description-display').style.display = 'none';
    row.querySelector('.description-edit').style.display = 'inline';

    // Hide edit button, show save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'none';
    row.querySelector('.save-btn').style.display = 'inline';
    row.querySelector('.cancel-btn').style.display = 'inline';
}

function cancelEdit(categoryId) {
    const row = document.querySelector(`tr:has(button[onclick*="editCategory(${categoryId})"])`);

    // Show display elements and hide edit elements
    row.querySelector('.name-display').style.display = 'inline';
    row.querySelector('.name-edit').style.display = 'none';
    row.querySelector('.description-display').style.display = 'inline';
    row.querySelector('.description-edit').style.display = 'none';

    // Show edit button, hide save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'inline';
    row.querySelector('.save-btn').style.display = 'none';
    row.querySelector('.cancel-btn').style.display = 'none';

    // Reset values
    const originalName = row.querySelector('.name-display').textContent;
    const originalDescription = row.querySelector('.description-display').textContent;
    row.querySelector('.name-edit').value = originalName;
    row.querySelector('.description-edit').value = originalDescription === 'No description' ? '' : originalDescription;
}

function saveCategory(categoryId) {
    const row = document.querySelector(`tr:has(button[onclick*="editCategory(${categoryId})"])`);
    const name = row.querySelector('.name-edit').value.trim();
    const description = row.querySelector('.description-edit').value.trim();

    if (!name) {
        showMessage('Category name cannot be empty', 'error');
        return;
    }

    fetch(`/auth/api/staff/categories/${categoryId}/update`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: name,
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Update display values
            row.querySelector('.name-display').textContent = name;
            row.querySelector('.description-display').textContent = description || 'No description';

            cancelEdit(categoryId);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error updating category', 'error');
    });
}

function deleteCategory(categoryId, categoryName) {
    if (confirm(`Are you sure you want to delete category "${categoryName}"? This action cannot be undone.`)) {
        fetch(`/auth/api/staff/categories/${categoryId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Remove the row from table
                const row = document.querySelector(`tr:has(button[onclick*="editCategory(${categoryId})"])`);
                row.remove();
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error deleting category', 'error');
        });
    }
}
</script>
{% endblock %}
