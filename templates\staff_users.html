{% extends "base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="staff-container">
    <h1>User Management</h1>
    
    <!-- Add User Form -->
    <div class="inventory-actions">
        <div class="inventory-search">
            <form method="post" style="display: flex; gap: 10px; align-items: end; width: 100%;">
                <div style="flex: 1;">
                    <label for="username" style="display: block; margin-bottom: 5px; font-weight: bold;">Username:</label>
                    <input type="text" id="username" name="username" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <div style="flex: 1;">
                    <label for="password" style="display: block; margin-bottom: 5px; font-weight: bold;">Password:</label>
                    <input type="password" id="password" name="password" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <div style="flex: 1;">
                    <label for="role" style="display: block; margin-bottom: 5px; font-weight: bold;">Role:</label>
                    <select id="role" name="role" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                        <option value="staff">Staff</option>
                        <option value="admin">Admin</option>
                        <option value="super_admin">Super Admin</option>
                    </select>
                </div>
                <button type="submit" style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; min-width: 120px;">Add User</button>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="inventory-list">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #aaa; font-weight: 400;">
            <thead>
                <tr>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">ID</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Username</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Role</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">{{ user.id }}</td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="username-display">{{ user.username }}</span>
                        <input type="text" class="username-edit" value="{{ user.username }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="role-display">
                            {% if user.role == 'super_admin' %}
                                <span style="background-color: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Super Admin</span>
                            {% elif user.role == 'admin' %}
                                <span style="background-color: #fd7e14; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Admin</span>
                            {% else %}
                                <span style="background-color: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Staff</span>
                            {% endif %}
                        </span>
                        <select class="role-edit" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                            <option value="staff" {% if user.role == 'staff' %}selected{% endif %}>Staff</option>
                            <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
                            <option value="super_admin" {% if user.role == 'super_admin' %}selected{% endif %}>Super Admin</option>
                        </select>
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        <div class="action-buttons">
                            <button class="edit-btn" onclick="editUser({{ user.id }})" style="background-color: #ffc107; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Edit</button>
                            <button class="save-btn" onclick="saveUser({{ user.id }})" style="display: none; background-color: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Save</button>
                            <button class="cancel-btn" onclick="cancelEdit({{ user.id }})" style="display: none; background-color: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Cancel</button>
                            {% if user.id != session.user_id %}
                            <button onclick="deleteUser({{ user.id }}, '{{ user.username }}')" style="background-color: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px;">Delete</button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Message Container for Notifications -->
<div id="message-container"></div>

{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
<style>
    .staff-container {
        width: 100%;
        margin: 0 auto;
        padding: 20px;
    }

    .inventory-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 20px 0;
        gap: 15px;
    }

    .inventory-search {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 1000px;
    }

    .inventory-list table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .inventory-list th,
    .inventory-list td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .inventory-list th {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .inventory-list tbody tr:hover {
        background-color: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
    }

    .action-buttons button {
        transition: background-color 0.2s;
    }

    .action-buttons button:hover {
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
<script>
function editUser(userId) {
    const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);
    
    // Hide display elements and show edit elements
    row.querySelector('.username-display').style.display = 'none';
    row.querySelector('.username-edit').style.display = 'inline';
    row.querySelector('.role-display').style.display = 'none';
    row.querySelector('.role-edit').style.display = 'inline';
    
    // Hide edit button, show save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'none';
    row.querySelector('.save-btn').style.display = 'inline';
    row.querySelector('.cancel-btn').style.display = 'inline';
}

function cancelEdit(userId) {
    const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);
    
    // Show display elements and hide edit elements
    row.querySelector('.username-display').style.display = 'inline';
    row.querySelector('.username-edit').style.display = 'none';
    row.querySelector('.role-display').style.display = 'inline';
    row.querySelector('.role-edit').style.display = 'none';
    
    // Show edit button, hide save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'inline';
    row.querySelector('.save-btn').style.display = 'none';
    row.querySelector('.cancel-btn').style.display = 'none';
    
    // Reset values
    const originalUsername = row.querySelector('.username-display').textContent;
    row.querySelector('.username-edit').value = originalUsername;
}

function saveUser(userId) {
    const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);
    const username = row.querySelector('.username-edit').value.trim();
    const role = row.querySelector('.role-edit').value;
    
    if (!username) {
        showMessage('Username cannot be empty', 'error');
        return;
    }
    
    fetch(`/auth/api/staff/users/${userId}/update`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            role: role
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Update display values
            row.querySelector('.username-display').textContent = username;
            
            // Update role badge
            const roleDisplay = row.querySelector('.role-display span');
            if (role === 'super_admin') {
                roleDisplay.textContent = 'Super Admin';
                roleDisplay.style.backgroundColor = '#dc3545';
            } else if (role === 'admin') {
                roleDisplay.textContent = 'Admin';
                roleDisplay.style.backgroundColor = '#fd7e14';
            } else {
                roleDisplay.textContent = 'Staff';
                roleDisplay.style.backgroundColor = '#28a745';
            }
            
            cancelEdit(userId);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error updating user', 'error');
    });
}

function deleteUser(userId, username) {
    if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
        fetch(`/auth/api/staff/users/${userId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Remove the row from table
                const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);
                row.remove();
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error deleting user', 'error');
        });
    }
}
</script>
{% endblock %}
